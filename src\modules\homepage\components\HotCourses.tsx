import React, { useState } from 'react';
import Image from 'next/image';
import Button from '@/components/ui/Button';
import { Course, Category } from '@/types/data';

interface HotCoursesProps {
  categories: Category[];
  courses: Course[];
  onCourseClick: (courseId: string) => void;
}

const HotCourses: React.FC<HotCoursesProps> = ({ 
  categories, 
  courses, 
  onCourseClick 
}) => {
  const [selectedCategory, setSelectedCategory] = useState('Tất cả');

  const handleCategoryClick = (category: string) => {
    setSelectedCategory(category);
  };

  return (
    <section className="w-full bg-global-11 py-12 sm:py-16 md:py-20 lg:py-24">
      <div className="w-full max-w-[1440px] mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col items-center gap-8 sm:gap-10 md:gap-12">
          <Button
            variant="primary"
            size="sm"
            className="bg-global-3 text-global-11 shadow-[0px_4px_30px_#46326644] hover:bg-global-4 font-bold border border-global-3"
            leftIcon="/images/img_frame.svg"
          >
            KHÓA HỌC
          </Button>
          <div className="text-center max-w-3xl">
            <h2 className="text-[24px] sm:text-[32px] md:text-[40px] lg:text-[48px] font-black text-global-4 leading-tight mb-4">
              Các khóa học Hot nhất 2025
            </h2>
            <p className="text-base sm:text-lg text-global-5 leading-relaxed">
              Khám phá những khóa học được yêu thích nhất với nội dung chất lượng cao và giảng viên chuyên nghiệp
            </p>
          </div>
          <div className="bg-global-10 rounded-[20px] p-3 flex flex-wrap gap-3 justify-center shadow-[0px_2px_8px_#00000008]">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => handleCategoryClick(category.name)}
                className={`
                  px-4 py-2.5 rounded-[16px] text-sm font-semibold
                  transition-all duration-300 ease-in-out
                  focus:outline-none focus:ring-2 focus:ring-button-1 focus:ring-opacity-50
                  min-h-[40px] inline-flex items-center justify-center
                  ${selectedCategory === category.name
                    ? 'bg-button-1 text-global-11 shadow-[0px_4px_12px_#ff816244] transform scale-105'
                    : 'text-global-4 bg-global-12 hover:bg-button-1 hover:text-global-11 hover:shadow-[0px_2px_8px_#ff816222] hover:scale-102 active:scale-95'
                  }
                `.trim().replace(/\s+/g, ' ')}
                aria-pressed={selectedCategory === category.name}
                role="tab"
              >
                {category.name}
              </button>
            ))}
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8 w-full">
            {courses.slice(0, 8).map((course) => (
              <div
                key={course.id}
                className={`
                  bg-global-12 rounded-[24px] overflow-hidden cursor-pointer group
                  shadow-[0px_4px_16px_#00000012] hover:shadow-[0px_8px_32px_#00000020]
                  transition-all duration-300 ease-in-out
                  hover:scale-105 hover:-translate-y-2
                  border border-global-10 hover:border-button-1/20
                  focus-within:ring-2 focus-within:ring-button-1 focus-within:ring-opacity-50
                `.trim().replace(/\s+/g, ' ')}
                onClick={() => onCourseClick(course.id)}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    onCourseClick(course.id);
                  }
                }}
              >
                <div className="relative overflow-hidden">
                  <Image
                    src={course.courseImage}
                    alt={course.title}
                    width={294}
                    height={204}
                    className="w-full h-[150px] sm:h-[180px] md:h-[204px] object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </div>
                <div className="p-5 flex flex-col gap-4">
                  <h3 className="text-base sm:text-lg md:text-xl font-bold text-global-4 line-clamp-2 leading-tight group-hover:text-global-3 transition-colors duration-200">
                    {course.title}
                  </h3>
                  <div className="flex justify-end">
                    <span className="bg-gradient-to-r from-global-3 to-global-4 text-global-11 text-sm font-bold px-4 py-2 rounded-[16px] shadow-[0px_2px_8px_#4a306d22]">
                      {course.price}
                    </span>
                  </div>
                  <div className="flex items-center justify-between pt-2 border-t border-global-10">
                    <div className="flex items-center gap-3">
                      <div className="relative">
                        <Image
                          src={course.instructorImage}
                          alt={course.instructor}
                          width={32}
                          height={32}
                          className="w-8 h-8 rounded-full ring-2 ring-global-10 group-hover:ring-button-1/30 transition-all duration-200"
                        />
                      </div>
                      <span className="text-sm text-global-5 truncate font-medium">
                        {course.instructor}
                      </span>
                    </div>
                    <div className="flex items-center gap-2 text-button-1 group-hover:text-global-3 transition-colors duration-200">
                      <span className="text-sm font-semibold">Chi tiết</span>
                      <Image
                        src="/images/img_arrow_right_yellow_900.svg"
                        alt="Arrow"
                        width={20}
                        height={20}
                        className="group-hover:translate-x-1 transition-transform duration-200"
                      />
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          <Button
            variant="outline"
            size="md"
            className={`
              border-2 border-global-3 text-global-3 bg-global-12
              hover:bg-global-3 hover:text-global-11 hover:border-global-3
              focus:ring-2 focus:ring-global-3 focus:ring-opacity-50
              shadow-[0px_2px_8px_#46326622] hover:shadow-[0px_4px_16px_#46326633]
              font-semibold px-8 py-3 min-w-[160px]
              transition-all duration-300 ease-in-out
            `.trim().replace(/\s+/g, ' ')}
          >
            Xem thêm
          </Button>
        </div>
      </div>
    </section>
  );
};

export default HotCourses;
